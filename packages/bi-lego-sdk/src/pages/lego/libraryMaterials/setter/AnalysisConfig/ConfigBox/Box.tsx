// @ts-nocheck
import { Dropdown, Tooltip } from '@blmcp/ui';
import { useCallback } from 'react';
import { ReactComponent as DragIcon } from '@/assets/lego/drag-icon.svg';
import { ReactComponent as MoreOutlinedIcon } from './moreOutlined.svg';
import { FieldType } from '../types';
import { Aggregate, AggregateType } from '../constant';
import { ItemTypes } from '../ItemTypes';
import { useTooltipShow } from './useTooltipShow';
import styles from './index.module.less';
import { sortIconMap } from './config';
import { Card } from './Card';
import { ColumnsType } from './';
import { MapTitle, getAggregateText, getQuickText } from '../utils';

interface BoxProps {
  col: ColumnsType;
  index: number;
  onClick: (index: number) => void;
  more: boolean;
  allowedDropEffect: string;
  getRootItems: (
    fieldType: FieldType,
    dataType: number,
    index: number,
    isAggr: boolean,
    summationComputeModeId: number,
    computeModeId: number,
    dateFormat: string,
    advanceComputeModeId: number,
    numberFormat: number,
    sortType?: string,
  ) => void;
  moveCard: (dragIndex: number, hoverIndex: number) => void;
  isDrag: boolean;
  setIsDrag: (flag: boolean) => void;
  deleteItem: (index: number) => void;
  // 右侧是否拖拽起来
  isDraggingField: boolean;
}

export const Box = ({
  col,
  index,
  onClick,
  getRootItems,
  more,
  moveCard,
  allowedDropEffect,
  isDrag,
  setIsDrag,
  deleteItem,
  isDraggingField,
  extraParams,
}: BoxProps) => {
  const AggregateText = getAggregateText(col);
  const quickText = getQuickText(col);
  const uniqKey = `${col.columnId}-${col?.id}`;

  const sortIcon = sortIconMap[col?.sortType ?? ''] ?? '';
  const { tooltipEnable, textRef } = useTooltipShow(
    AggregateText + col.title + quickText,
    col?.sortType,
    more,
  );
  const { tooltipEnable: aliasTooltipEnable, textRef: aliasRef } =
    useTooltipShow(col.alias, col?.sortType, more);

  console.log(
    col,
    'col-------',
    col?.alias ? 1 : 2,
    col.alias,
    'tooltipEnable',
    tooltipEnable,
    aliasTooltipEnable,
    'aliasTooltipEnable',
    textRef?.current,
    AggregateText + col.title + quickText,
    AggregateText,
    quickText,
  );
  const ColumnDOM = (
    <div className={styles['column']} key={uniqKey}>
      {more ? <DragIcon className={'operation-drag'} /> : null}
      {sortIcon}
      {col?.alias ? (
        <Tooltip
          title={
            aliasTooltipEnable ? (
              <>
                {col?.alias}
                <br />
                原字段名：{AggregateText}
                {col.title}
                {quickText}
              </>
            ) : (
              `原字段名：${AggregateText}${col.title}${quickText}`
            )
          }
        >
          <span className={styles.text} ref={aliasRef}>
            {col?.alias}
          </span>
        </Tooltip>
      ) : (
        <span className={styles.text} ref={textRef}>
          {AggregateText}
          {col.title}
          {quickText}
        </span>
      )}
      <Dropdown
        menu={{
          items: getRootItems(
            col.fieldType,
            col.dataType,
            index,
            col.isAggr,
            col.summationComputeModeId,
            col.computeModeId,
            col.dateFormat + '',
            col.advanceComputeModeId,
            col.numberFormat,
            col.sortType,
          ),
          onClick: onClick(index),
        }}
      >
        <a
          onClick={(e) => e.preventDefault()}
          className={styles['operation-menu']}
        >
          <div id={'__OperationId'} className={styles['new-guide-wrap']}></div>
          <MoreOutlinedIcon className={styles['operation']} />
        </a>
      </Dropdown>
    </div>
  );
  const TooltipDOM =
    tooltipEnable && !col?.alias ? (
      <Tooltip
        title={
          isDrag
            ? ''
            : `${AggregateText}
          ${col.title}
          ${quickText}`
        }
        mouseLeaveDelay={0}
      >
        {ColumnDOM}
      </Tooltip>
    ) : (
      ColumnDOM
    );

  const setDragFlag = useCallback(
    (isDragging: boolean) => {
      setIsDrag(isDragging);
    },
    [setIsDrag],
  );

  return (
    <Card
      key={uniqKey}
      index={index}
      id={uniqKey}
      moveCard={moveCard}
      dragType={ItemTypes.CARD + allowedDropEffect}
      allowedDropEffect={allowedDropEffect}
      setDragFlag={setDragFlag}
      deleteItem={deleteItem}
      isDraggingField={isDraggingField}
      extraParams={extraParams}
    >
      {TooltipDOM}
    </Card>
  );
};
