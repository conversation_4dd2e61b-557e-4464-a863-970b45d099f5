import React, { useEffect, useState } from 'react';
import { Tooltip } from '@blmcp/ui';
import { getSelectComponentNode } from '@/pages/lego/utils';
import styles from '../index.module.less';

// 需要过滤实时数据集的物料
const TREND_COMPONENTS = ['Card'];

/**
 * 判断当前物料是否在要过滤实时数据集范围内
 */
export const useIsTrendComponent = () => {
  const [isTrendComponent, setIsTrendComponent] = useState(false);

  useEffect(() => {
    const checkComponentType = () => {
      const currentNode = getSelectComponentNode();
      if (currentNode?.componentName) {
        setIsTrendComponent(
          TREND_COMPONENTS.includes(currentNode.componentName),
        );
      }
    };
    checkComponentType();
  }, []);

  return isTrendComponent;
};

/**
 * 置灰逻辑于tips提示，用于Select组件的options配置中
 */
export const useTrendComponentDataSourceOptions = (dataSourceList: any[]) => {
  const isTrendComponent = useIsTrendComponent();

  return dataSourceList.map((item) => {
    const shouldDisable =
      isTrendComponent && [1, 2, 3].includes(item?.dataUpdateType);
    const tooltipText = shouldDisable
      ? `${item.name}，实时数据集不支持使用趋势指标卡组件`
      : item.name;

    return {
      label: item.name,
      value: item.id,
      disabled: shouldDisable,
      title: tooltipText,
    };
  });
};

/**
 * 支持置灰选项的tooltip提示
 */
export const useTrendComponentOptionRender = () => {
  return (option: any) => {
    const { disabled, title } = option.data;

    const optionContent = (
      <div
        className={
          styles[disabled ? 'dataset-input-disabled' : 'dataset-input']
        }
      >
        {option.label}
      </div>
    );

    if (disabled && title) {
      return (
        <Tooltip title={title} placement="top">
          <div
            className={
              styles[disabled ? 'dataset-input-disabled' : 'dataset-input']
            }
          >
            {option.label}
          </div>
        </Tooltip>
      );
    }
    return optionContent;
  };
};
