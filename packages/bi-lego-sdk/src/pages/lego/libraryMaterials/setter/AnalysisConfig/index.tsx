// @ts-nocheck
/**
 *
 * Setter
 *
 */
import { useCallback, useEffect, useRef, useState } from 'react';

import { intersection, isNumber, flattenDeep, uniq } from 'lodash-es';
import { Modal } from '@blmcp/ui';
import { locale } from '@/pages/lego/components/GuideTooltip';
import { NewGuide } from '@/pages/lego/components/NewGuide';
import { generateRandomNumber } from '@/pages/lego/utils';
import { SortType } from '@/pages/lego/components/types';

import { ReactComponent as WarningIcon } from '@/assets/lego/warning.svg';
import { setPreviousDataSource } from '../../module/utils';
import { TopDnDProvider } from '../TopDnDProvider';
import { DataSourcePanel } from './DataSourcePanel';
import { ConfigBox } from './ConfigBox';
import { CalcType, ItemData, SetterData } from './types';
import { getRateMenuData } from './utils';
import styles from './index.module.less';
import { NumberFormatModal, ModalRef } from './NumberFormatModal';
import { FieldAliasModal, AliasModalRef } from './FieldAliasModal';

const { confirm } = Modal;

interface ListItem {
  key: string;
  label: string;
  placeholder?: string;
  onlyOne?: boolean;
  rateDependKey?: string;
  combineModeDim?: CalcType[];
  combineModeIndex?: CalcType[];
  ignoreSetDefaultComputeType?: boolean;
  dateFormatDisabled?: boolean;
  numberFormatConfig?: boolean; //  数据格式功能启用
  numberFormatIndexOnly?: boolean; // 只格式化指标类
  disabledSortConfig?: boolean; // 只格式化指标类
  disabledDrop?: string; // 当配置的字段存在多个选项时，
  onlyOneLimit?: string; // 限制配置的选项有值，
  hideLabelSuffix?: boolean; // 隐藏标签名称后缀
  hideRate?: boolean; // 隐藏同环比额增长率
  sortHideDepends?: string; // 排序设置隐藏所依赖的字段
  fieldAliasConfig?: boolean; // 字段别名禁用
}
interface AnalysisConfigProps {
  onChange: (data: SetterData) => void;
  value: SetterData;
  list: ListItem[];
  dimDisabled: boolean;
  indexDisabled: boolean;
  dateOnly: boolean;
  dateDisabled?: boolean;
  headSortFlag?: boolean; // 头部排序标识，例如交叉表头部排序标识
  dataSetDisabled?: boolean; // 数据集不可用
  field: any; //  lowcodeEngine 的field
  // 常量字段禁用
  aggDisabled?: boolean;
  numDisabled?: boolean;
  textDisabled?: boolean;
  saveSwitchDataSourceId?: boolean;
}
const extraAdvanceComputeModeIds = ['30'];
const TableSheetHeadSortKey = 'firstHeadSortField';
const keys = ['contrastInfo', 'measureInfo'];

//TODO: 交叉表格，需要判断，对比列，和数值列的拖入顺序，来决定展示顺序。 start
export const handleTableSheetHeadSort =
  (headSortFlag = false) =>
  (newDataSetConfig: SetterData) => {
    if (headSortFlag) {
      const originalKeys = Object.keys(newDataSetConfig).filter(
        (key) => newDataSetConfig[key]?.length > 0,
      );
      const intersectionKey = intersection(keys, originalKeys);
      if (intersectionKey.length === 1) {
        newDataSetConfig[TableSheetHeadSortKey] =
          intersectionKey[0] === keys[0] ? 1 : 0;
        return newDataSetConfig;
      } else {
        return newDataSetConfig;
      }
    } else {
      return newDataSetConfig;
    }
  };
//TODO: 交叉表格，需要判断，对比列，和数值列的拖入顺序，来决定展示顺序。 end

const getOperationListField = (field) => {
  const propsField = field.parent;
  const operationCols = propsField.getPropValue('operationCols');
  const { operationList = [] } = operationCols ?? {};
  const variableRegex = /\[([^[\]]+)\]/g;
  const urls = operationList
    .map((element) => {
      return element.url;
    })
    .filter(Boolean)
    .map((url: string) => {
      const fields = url.match(variableRegex);
      const fieldsArr = fields?.map((fields) =>
        fields.slice(1, fields.length - 1),
      );
      return fieldsArr;
    });
  return { urls: uniq(flattenDeep(urls)), operationList };
};

export const AnalysisConfig = ({
  onChange,
  value,
  subDOM,
  list,
  dimDisabled,
  indexDisabled,
  dateOnly,
  dateDisabled,
  headSortFlag,
  dataSetDisabled,
  aggDisabled,
  field,
  numDisabled,
  textDisabled,
  saveSwitchDataSourceId,
}: AnalysisConfigProps) => {
  const { event } = window.AliLowCodeEngine || {};
  const setting: SetterData = value ?? {};
  // 当前的dataSourceId 为了实现，切换数据源，并且，拖拽进新的数据源字段，清空拖拽区域
  const currentDataSourceId = setting?.dataSourceId;
  const draggingRef = useRef<boolean>(false);
  const modalRef = useRef<ModalRef>({});
  const aliasModalRef = useRef<AliasModalRef>({});
  const [steps, setStep] = useState([]);
  const propsField = field.parent;

  const getChangeValue = handleTableSheetHeadSort(headSortFlag);
  useEffect(() => {
    const intervalId = setInterval(() => {
      const firstElement = document?.querySelector('.lc-borders-selecting');
      if (firstElement) {
        const step = [
          {
            title: '调整布局',
            content: '可任意改变组件位置和宽度。',
            placement: 'bottom',
            disableBeacon: true,
            showSkipButton: true,
            target: document?.querySelector('.lc-borders-selecting'),
            locale,
          },
          {
            title: '选择数据集',
            content: '点击下拉选择框可切换数据集。',
            placement: 'left',
            target: '#__DataSource_Panel',
            disableBeacon: true,
            showSkipButton: true,
            locale,
          },
          {
            title: '搜索字段',
            content: '输入关键字查找字段名称。',
            placement: 'left',
            target: '#__Search_DataSource',
            disableBeacon: true,
            showSkipButton: true,
            locale,
          },

          {
            title: '配置区',
            content: '将右侧字段拖入组件配置区。',
            placement: 'left',
            target: '#__Drag_Panel',
            disableBeacon: true,
            showSkipButton: true,
            locale: { last: '完成' },
          },
        ];
        setStep(step);
        clearInterval(intervalId);
      }
    }, 500);
  }, []);

  // 重置sort

  const resetItemSort = useCallback(
    (type: string) => {
      const result: Record<string, ItemData[]> = {};
      const key = type;
      const isSorted = [SortType.ASC, SortType.DESC];
      // 更改原始值
      const itemConfig = setting[key] as ItemData[];
      itemConfig?.forEach((item) => {
        // 设置为最新的排序值
        if (isSorted.includes(item?.sortType as SortType)) {
          item.sortType = SortType.NONE;
        }
      });
      result[key] = itemConfig;
      const nextState = Object.assign(setting, result);
      onChange(nextState);
    },
    [onChange],
  );

  const linkageOperation = useCallback(
    (type: string, itemConfig: any, freeze = true) => {
      if (type === 'contrastInfo') {
        // 这里展示的和插件进行通信，事件规则是插件名 + 方法
        event.emit('operationColumnConfig.bindEvent', freeze);
      }
      if (type === 'dimensionInfo') {
        // 这里展示的和插件进行通信，事件规则是插件名 + 方法

        event.emit('operationDimChange.bindEvent', itemConfig);
      }
    },
    [],
  );

  // 上一个dataID
  const add = (data: ItemData) => {
    const { dropType = '', onlyOne, dataSourceId, insertIndex, ...rest } = data;

    const item = setting[dropType] as ItemData[];
    if (dataSourceId !== currentDataSourceId) {
      // 切换了数据源，则执行清空操作,
      onChange(getChangeValue({ dataSourceId, [dropType]: [rest] }));
      linkageOperation(dropType, [rest]);
      const operationList = getOperationListField(field).operationList;
      // 同时清空，操作列，如果有的话
      if (operationList?.length > 0 && currentDataSourceId) {
        propsField.setPropValue('operationCols', {
          freeze: true,
          operationList: [],
        });
      }

      setPreviousDataSource(dataSourceId);
      return;
    }
    // 执行单一替换
    if (onlyOne) {
      const result: Record<string, ItemData[]> = {};
      // 删除的是选过日期类型的，日期维度
      if (setting?.[dropType]?.[0]?.dateFormat) {
        const depandDateKeys = list
          .filter((item) => item.rateDependKey)
          .map((item) => item.key);
        depandDateKeys?.forEach((key) => {
          const rateInfoList = setting[key] as ItemData[];
          rateInfoList?.forEach((item) => {
            // 是占比选项，保留非占比，重置
            if (
              !extraAdvanceComputeModeIds.includes(
                item?.advanceComputeModeId ?? '',
              )
            ) {
              item.advanceComputeModeId = undefined;
              item.numberFormat = undefined;
            }
          });
          result[key] = rateInfoList;
        });
      }
      result[dropType] = [rest];
      const nextState = Object.assign(setting, result);
      onChange(getChangeValue(nextState));
      linkageOperation(dropType, [rest]);
    }
    // 如果有这个元素
    else if (item?.length > 0) {
      // 已经存在的，则根据是否有index，来进行插入
      if (isNumber(insertIndex)) {
        // 没有任何insertIndex,直接放在最后一个
        const newItem = [...item];
        newItem.splice(insertIndex + 1, 0, {
          ...rest,
          id: generateRandomNumber(),
        });
        const nextState = Object.assign(setting, {
          [dropType]: newItem,
        });
        onChange(getChangeValue(nextState));
        linkageOperation(dropType, newItem);
      } else {
        // 没有任何insertIndex,直接放在最后一个
        const newItem = [...item, { ...rest, id: generateRandomNumber() }];
        const nextState = Object.assign(setting, {
          [dropType]: newItem,
        });
        onChange(getChangeValue(nextState));
        linkageOperation(dropType, newItem);
      }
    } else {
      // 交叉表，交叉列特殊处理
      if (dropType === 'contrastInfo' && setting?.[dropType]?.length === 0) {
        resetItemSort('measureInfo');
      }
      // 交叉表，交叉列特殊处理

      const nextState = Object.assign(setting, {
        [dropType]: [rest],
      });

      onChange(getChangeValue(nextState));
      linkageOperation(dropType, [rest]);
    }
  };

  const deleteBox = (type: string) => (index: number) => {
    const itemConfig = setting[type] as ItemData[];

    const deleteField = () => {
      const deleteItem = itemConfig.splice(index, 1);

      const result: Record<string, ItemData[]> = {};
      // 删除的是选过日期类型的，日期维度,交叉表的变更不用清空同环比
      if (deleteItem?.[0]?.dateFormat && type !== 'contrastInfo') {
        const depandDateKeys = list
          .filter((item) => item.rateDependKey)
          .map((item) => item.key);
        depandDateKeys?.forEach((key) => {
          const rateInfoList = setting[key] as ItemData[];
          rateInfoList?.forEach((item) => {
            // 是占比选项，保留非占比，重置
            if (
              !extraAdvanceComputeModeIds.includes(
                item?.advanceComputeModeId ?? '',
              )
            ) {
              item.advanceComputeModeId = undefined;
              item.numberFormat = undefined;
            }
          });
          result[key] = rateInfoList;
        });
      }
      result[type] = itemConfig;
      const nextState = Object.assign(setting, result);
      onChange(getChangeValue({ ...nextState }));
      linkageOperation(type, itemConfig, itemConfig?.length > 0);
    };
    const useFields = getOperationListField(field).urls;
    if (
      useFields.includes(itemConfig[index].title) &&
      type === 'dimensionInfo'
    ) {
      confirm({
        title: '确认删除吗？',
        icon: <WarningIcon className={styles['warning-icon']} />,
        content:
          '该字段在图表中配置了操作列，删除字段将导致操作列不可用，是否继续？',
        autoFocusButton: null,
        onOk() {
          deleteField();
        },
        onCancel() {},
      });

      return;
    } else {
      deleteField();
    }
  };
  const setNumberFormat = (type: string) => (index: number) => {
    const itemConfig = setting?.[type]?.[index] as ItemData;
    modalRef?.current?.showModal?.(
      type,
      index,
      itemConfig?.numFormatInfo ?? '',
    );
  };
  // 修改别名弹窗
  const setFieldAlias = (type: string) => (index: number) => {
    const itemConfig = setting?.[type]?.[index] as ItemData;
    aliasModalRef?.current?.showModal?.(type, index, itemConfig);
  };
  // 修改box-
  const modifyBox =
    (type: string) => (index: number, key: string, value: string) => {
      const result: Record<string, ItemData[]> = {};
      // 如果是改变的日期类型，则清除依赖日期的字段中，同环比的数据。
      if (key === 'dateFormat') {
        const depandDateKeys = list
          ?.filter((item) => item.rateDependKey)
          .map((item) => item.key);
        depandDateKeys?.forEach((key) => {
          const rateInfoList = setting[key] as ItemData[];
          rateInfoList?.forEach((item) => {
            // 是占比选项，保留非占比，重置
            if (
              !extraAdvanceComputeModeIds.includes(
                item?.advanceComputeModeId ?? '',
              )
            ) {
              item.advanceComputeModeId = undefined;
              item.numberFormat = undefined;
            }
          });
          result[key] = rateInfoList;
        });
      }
      // 更改原始值
      const itemConfig = setting[type] as ItemData[];
      itemConfig[index][key] = value;

      result[type] = itemConfig;

      const nextState = Object.assign(setting, result);
      onChange(nextState);
    };

  // 修改Sort 清除其他sort 只保留最新设置的sort
  const onSortClick = (type: string) => (index: number, value: SortType) => {
    const result: Record<string, ItemData[]> = {};

    const isSorted = [SortType.ASC, SortType.DESC];
    list.forEach((itemList) => {
      const key = itemList.key;
      // 更改原始值
      const itemConfig = setting[key] as ItemData[];
      itemConfig?.forEach((item, idx: number) => {
        // 设置为最新的排序值
        if (type === key && index === idx) {
          item.sortType = value;
        } else if (isSorted.includes(item?.sortType)) {
          item.sortType = SortType.NONE;
        }
      });
      result[key] = itemConfig;
    });
    const nextState = Object.assign(setting, result);
    onChange(nextState);
  };

  // 更改数据格式
  const setNumFormat = (type: string, index: number, value: string) => {
    const result: Record<string, ItemData[]> = {};
    // 更改原始值
    const itemConfig = setting[type] as ItemData[];
    itemConfig[index]['numFormatInfo'] = value;
    result[type] = itemConfig;
    const nextState = Object.assign(setting, result);
    onChange(nextState);
  };
  const setFiledAlias = (type: string, index: number, value: any) => {
    const result: Record<string, ItemData[]> = {};
    // 更改原始值
    const itemConfig = setting[type] as ItemData[];
    itemConfig[index]['alias'] = value?.alias;
    itemConfig[index]['tips'] = value?.tips;
    result[type] = itemConfig;
    const nextState = Object.assign(setting, result);
    onChange(nextState);
  };

  // 移动位置
  const moveCard =
    (type: string) => (dragIndex: number, hoverIndex: number) => {
      const itemConfig = setting[type] as ItemData[];
      const result: Record<string, ItemData[]> = {};
      const item = itemConfig.splice(dragIndex, 1);
      itemConfig.splice(hoverIndex, 0, item[0]);

      result[type] = itemConfig;
      const nextState = Object.assign(setting, result);
      onChange(nextState);
    };

  const setDataSourceId = (id: number) => {
    const nextState = { dataSourceId: id };
    onChange(nextState);
  };

  const getRateConfig = (rateDependKey = '', hideRate = false) => {
    // 获取同环比配置
    const getRateInfo = (rateDependKey = '', hideRate: boolean) => {
      // 单一维度字段决定的
      if (rateDependKey) {
        const DependValue = setting[rateDependKey] as ItemData[];
        const dateField = DependValue?.find((val) => val.dataType === 2);
        // 有日期维度，并且日期维度选择了日期类型
        if (dateField && dateField.dateFormat) {
          return getRateMenuData(dateField.dateFormat, hideRate);
        }
        return [];
      } else {
        return [];
      }
    };
    const isArr = Array.isArray(rateDependKey);
    if (isArr) {
      let result: any = [];
      // 由多个字段决定的
      rateDependKey.some((itemRateDependKey) => {
        const rateInfo = getRateInfo(itemRateDependKey, hideRate);
        if (rateInfo?.length > 0) {
          result = rateInfo;
          return true;
        } else {
          return false;
        }
      });
      return result;
    } else {
      // 单一维度字段决定的
      return getRateInfo(rateDependKey, hideRate);
    }
  };

  return (
    <TopDnDProvider>
      <div className={styles['setter-wrap']}>
        <div className={styles['data-source-setter-wrap']}>
          {/* 左侧列表 */}
          <div className={styles['panel-all']}>
            <div style={{ position: 'relative' }}>
              <div id="__Drag_Panel" className={styles['new-guide-wrap']}></div>
              {list?.map((itemList) => {
                const disabled = itemList?.disabledDrop
                  ? setting?.[itemList?.disabledDrop]?.length >= 2
                  : false;
                const onlyOne = itemList?.onlyOneLimit
                  ? setting?.[itemList?.onlyOneLimit]?.length >= 1
                  : false;

                const suffix =
                  onlyOne || itemList?.onlyOne || itemList?.hideLabelSuffix
                    ? ''
                    : '（可多选）';
                return (
                  <div key={itemList.key}>
                    <div
                      className={
                        disabled ? styles['title-disabled'] : styles['title']
                      }
                    >
                      {itemList.label}
                      {suffix}
                    </div>
                    <div className={styles['config-box-item']}>
                      <ConfigBox
                        draggingRef={draggingRef}
                        onlyOne={onlyOne || itemList?.onlyOne}
                        allowedDropEffect={itemList.key}
                        columns={setting?.[itemList.key] ?? []}
                        deleteBox={deleteBox(itemList.key)}
                        setNumberFormat={setNumberFormat(itemList.key)}
                        setFieldAlias={setFieldAlias(itemList.key)}
                        modifyBox={modifyBox(itemList.key)}
                        onSortClick={onSortClick(itemList.key)}
                        moveCard={moveCard(itemList.key)}
                        placeholder={itemList.placeholder}
                        combineModeDim={itemList?.combineModeDim ?? {}}
                        combineModeIndex={itemList?.combineModeIndex ?? {}}
                        rateConfig={getRateConfig(
                          itemList?.rateDependKey,
                          itemList?.hideRate,
                        )}
                        ignoreSetDefaultComputeType={
                          itemList?.ignoreSetDefaultComputeType
                        }
                        dateFormatDisabled={itemList?.dateFormatDisabled}
                        numberFormatConfig={itemList?.numberFormatConfig}
                        fieldAliasConfig={itemList?.fieldAliasConfig}
                        numberFormatIndexOnly={itemList?.numberFormatIndexOnly}
                        disabledSortConfig={
                          itemList?.disabledSortConfig ||
                          ((setting?.[itemList?.sortHideDepends ?? '']?.length >
                            0) as ItemData[])
                        }
                        disabled={disabled}
                      />
                    </div>
                  </div>
                );
              })}
            </div>
            {subDOM}
          </div>
          {/* 右侧数据集 */}
          <div className={styles.panel} id="__DataSource_Panel">
            <div className={styles['title']}>数据集</div>
            <DataSourcePanel
              draggingRef={draggingRef}
              setting={setting}
              add={add}
              dimDisabled={dimDisabled}
              indexDisabled={indexDisabled}
              setDataSourceId={setDataSourceId}
              dataSourceId={setting?.dataSourceId}
              dateOnly={dateOnly}
              dateDisabled={dateDisabled}
              aggDisabled={aggDisabled}
              dataSetDisabled={dataSetDisabled}
              numDisabled={numDisabled}
              textDisabled={textDisabled}
              saveSwitchDataSourceId={saveSwitchDataSourceId}
            />
          </div>
        </div>

        <NewGuide steps={steps} localKey="New_Guide_Lego_DataSource" />
        <NumberFormatModal ref={modalRef} setNumFormat={setNumFormat} />
        <FieldAliasModal ref={aliasModalRef} setFiledAlias={setFiledAlias} />
      </div>
    </TopDnDProvider>
  );
};
