import { useEffect, useRef, useState } from 'react';

// tooltip 按需显示
export const useTooltipShow = (text: string, sortType = '', more = false) => {
  const [tooltipEnable, setTooltipEnable] = useState(false);
  const textRef = useRef<HTMLSpanElement>(null);
  console.log(
    '22222col-------',
    tooltipEnable,
    textRef?.current,
    'textRef?.current',
    textRef?.current?.scrollWidth,
    textRef?.current?.offsetWidth,
    text,
    'sortType',
    sortType,
  );
  // tooltipEnable 判断
  useEffect(() => {
    console.log('33333col-------', textRef?.current);
    const scrollWidth = textRef?.current?.scrollWidth ?? 0;
    const offsetWidth = textRef?.current?.offsetWidth ?? 0;
    if (scrollWidth > offsetWidth) {
      setTooltipEnable(true);
    } else {
      setTooltipEnable(false);
    }
  }, [text, sortType, more]);

  return { tooltipEnable, textRef };
};
