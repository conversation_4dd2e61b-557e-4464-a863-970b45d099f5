import React from 'react';
import { Input, Tooltip } from '@blmcp/ui';
// import { isHubble } from '@/utils/hubble';
import styles from './index.module.less';
import { InfoCircleOutlined } from '@ant-design/icons';

interface SetterProps {
  value: any;
  onChange: any;
  isDate?: boolean;
}

export default ({ onChange, value, isDate }: SetterProps) => {
  return (
    <div
      className={styles['setter-date-picker-default-value']}
      style={{ marginLeft: '-10px' }}
    >
      <div className={styles['title']}>
        筛选器名称
        <Tooltip
          title={`若不填写，保存后将显示默认字段名称${
            isDate ? '：分区日期' : ''
          }`}
        >
          <InfoCircleOutlined />
        </Tooltip>
      </div>
      <div className={styles['span-text']}>
        <Input
          value={value}
          allowClear
          placeholder="请输入"
          showCount
          maxLength={50}
          onChange={(event) => {
            onChange(event.target.value);
          }}
        ></Input>
      </div>
    </div>
  );
};
