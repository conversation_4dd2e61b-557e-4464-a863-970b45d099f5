// @ts-nocheck
import React from 'react';
import { DownOutlined } from '@ant-design/icons';
import { PluginProps } from '@alilc/lowcode-types';
// import { handleRowWidth, config } from '@alilc/lowcode-engine';
import { throttle } from 'lodash-es';
import { locale } from '@/pages/lego/components/GuideTooltip';
import { NewGuide } from '@/pages/lego/components/NewGuide';
import {
  getSelectComponentNode,
  walkFromParentNode,
  getCurrentTabSlot,
} from '@/pages/lego/utils';
import { reportBusinessMonitor } from '@/utils/eventTracking';
import IconOfPane from '../Icon';
import Component from '../components/Component';
import ComponentManager from '../store';

import transform, {
  getTextReader,
  SortedGroups,
  Text,
  StandardComponentMeta,
  SnippetMeta,
  createI18n,
} from '../utils/transform';
import { ReactComponent as CardIcon } from './icon/ic-card.svg';
import { ReactComponent as ChartPieIcon } from './icon/ic-chartPie.svg';
import { ReactComponent as FilterIcon } from './icon/ic-filter.svg';
import { ReactComponent as TableIcon } from './icon/ic-table.svg';
import { ReactComponent as TextIcon } from './icon/ic-text.svg';
import { ReactComponent as DragIcon } from './icon/ic-drag.svg';
import { ReactComponent as TabsIcon } from './icon/ic-tabs.svg';
import './index.less';
import sdkConfig from '@/tool/config';
import { filterComponents } from '@/pages/lego/config';

function debounce(fn: any) {
  return fn;
}

const material = () => window.AliLowCodeEngine.material;
const common = () => window.AliLowCodeEngine.common;
const project = () => window.AliLowCodeEngine.project;
const event = () => window.AliLowCodeEngine.event;
const editor = () => window.AliLowCodeEngine.editor;
const isNewEngineVersion = () => !!material();

const store = new ComponentManager();

interface ComponentPaneProps extends PluginProps {
  [key: string]: any;
}

interface ComponentPaneState {
  groups: SortedGroups[];
  filter: SortedGroups[];
  keyword: string;
}

export default class ComponentPane extends React.Component<
  ComponentPaneProps,
  ComponentPaneState
> {
  static displayName = 'LowcodeComponentPane';

  static defaultProps = {
    lang: 'zh_CN',
  };
  static steps = [
    {
      target: '#__ComponentListGuide', // 要引导的元素，可以是CSS选择器、React 组件等
      title: '组件区域', // 步骤标题
      content: '点击或拖拽所要的组件', // 步骤内容
      placement: 'bottom', // 引导气泡的位置
      disableBeacon: true,
      showSkipButton: false,
      locale,
    },
  ];

  state: ComponentPaneState = {
    groups: [],
    filter: [],
    keyword: '',
  };

  store = store;

  t: (input: Text) => string;

  aliLowCodeEngineEditor = editor();

  materielGroup = [
    {
      groupName: '指标卡',
      groupIcon: CardIcon,
      groupId: 1,
      materiels: [],
    },
    {
      groupName: '表格',
      groupIcon: TableIcon,
      groupId: 2,
      materiels: [],
    },
    {
      groupName: '图表',
      groupIcon: ChartPieIcon,
      groupId: 3,
      materiels: [],
    },
    {
      groupName: '筛选器',
      groupIcon: FilterIcon,
      groupId: 4,
      materiels: [],
    },
    {
      groupName: '文本',
      groupIcon: TextIcon,
      groupId: 5,
      materiels: [],
    },
    {
      groupName: '选项卡',
      groupIcon: TabsIcon,
      groupId: 6,
      materiels: [],
    },
    {
      groupName: '默认分组',
      groupIcon: '',
      groupId: 'default',
      materiels: [],
    },
  ];

  getStrKeywords: (keywords: Text[]) => string;

  getKeyToSearch(c: StandardComponentMeta | SnippetMeta) {
    const strTitle = this.t(c.title);
    const strComponentName = this.t((c as SnippetMeta).schema?.componentName);
    const strDescription = 'description' in c ? this.t(c.description) : '';
    const strKeywords =
      'keywords' in c ? this.getStrKeywords(c.keywords || []) : '';
    return `${strTitle}#${strComponentName}#${strDescription}#${strKeywords}`.toLowerCase();
  }

  getFilteredComponents = debounce(() => {
    const { groups = [], keyword } = this.state;
    if (!keyword) {
      this.setState({
        filter: groups,
      });
      return;
    }

    const filter = groups.map((group) => ({
      ...group,
      categories: group.categories
        .map((category) => ({
          ...category,
          components: category.components.filter((c) => {
            let keyToSearch = this.getKeyToSearch(c);
            if (c.snippets) {
              c.snippets.map((item) => {
                keyToSearch += `_${this.getKeyToSearch(item)}`;
              });
            }
            return keyToSearch.includes(keyword);
          }),
        }))
        .filter((c) => c?.components?.length),
    }));

    this.setState({
      filter,
    });
  }, 200);

  constructor(props) {
    super(props);
    this.t = getTextReader(props.lang);
    this.getStrKeywords = (keywords: Text[]): string => {
      if (typeof keywords === 'string') {
        return keywords;
      }
      if (keywords && Array.isArray(keywords) && keywords.length) {
        return keywords.map((keyword) => this.t(keyword)).join('-');
      }
      return '';
    };
    // console.log(this.getStrKeywords, '🌈🌈=========this.getStrKeywords');
  }

  componentDidMount() {
    try {
      const { editor } = this.props;
      if (!editor) {
        this.initComponentList();
        return;
      }
      const assets = isNewEngineVersion()
        ? material().getAssets()
        : editor.get('assets');
      if (assets) {
        this.initComponentList();
      } else {
        console.warn(
          '[ComponentsPane]: assets not ready, wait for assets ready event.',
        );
      }
      if (isNewEngineVersion()) {
        event().on('trunk.change', this.initComponentList.bind(this));
        material().onChangeAssets(this.initComponentList.bind(this));
      } else {
        editor.on('trunk.change', this.initComponentList.bind(this));
        editor.once('editor.ready', this.initComponentList.bind(this));
        editor.on(
          'designer.incrementalAssetsReady',
          this.initComponentList.bind(this),
        );
      }
    } catch (e) {
      console.log(e, '========= componentDidMount error');
    }
  }

  /**
   * 初始化组件列表
   * TODO: 无副作用，可多次执行
   */
  initComponentList() {
    try {
      const { editor } = this.props;
      const rawData = isNewEngineVersion()
        ? material().getAssets()
        : editor.get('assets');

      const meta = transform(rawData, this.t);

      const { groups, snippets } = meta;

      this.store.setSnippets(snippets);

      this.setState({
        groups,
        filter: groups,
      });
    } catch (e) {
      console.log('=========initComponentList error');
    }
  }

  registerAdditive = (shell: HTMLDivElement | null) => {
    if (!shell || shell.dataset.registered) {
      return;
    }

    function getSnippetId(elem: any) {
      if (!elem) {
        return null;
      }
      while (shell !== elem) {
        if (elem.classList.contains('snippet')) {
          return elem.dataset.id;
        }
        elem = elem.parentNode;
      }
      return null;
    }

    const { editor } = this.props;
    const designer = !isNewEngineVersion() ? editor?.get('designer') : null;
    const _dragon = isNewEngineVersion()
      ? common().designerCabin.dragon
      : designer?.dragon;
    if (!_dragon || (!isNewEngineVersion() && !designer)) {
      return;
    }

    // eslint-disable-next-line
    const click = (e: Event) => {
      const id = getSnippetId(e.target);
      if (!id) {
        return false;
      }
      const container = this.store.getSnippetById(id);
      const fdCellStyle: any = {
        backgroundColor: 'rgba(255,255,255,1)',
      };
      // 为表格类物料容器添加overflow,解决列撑开画布问题
      try {
        const { components = [] } = window.LeopardWebQbiMeta || {};
        const curr = components.find(
          (c) => c.componentName === container.componentName,
        );
        if (
          (curr &&
            curr.componentBehavior &&
            curr.componentBehavior.componentType === 2) ||
          curr.componentName === 'Rank'
        ) {
          fdCellStyle['overflow'] = 'hidden';
        }
      } catch (e) {}
      container.props = container.props || {};
      container.props.dataSetConfig = {};
      let cellData = {
        componentName: 'FDCell',
        props: {
          align: 'center',
          verAlign: 'top',
          style: fdCellStyle,
          id: '__FDCell_New_Guide_ID',
        },
        children: [container],
      };
      let rowData = {
        componentName: 'FDRow',
        children: [cellData],
      };

      // 是否tab 点击插入
      let TabNode = null;
      // 默认取page, 如果是在tab组件内，则取tab 的slot
      let root = project().currentDocument.root;
      const filtersMarkers = filterComponents;
      if (filtersMarkers.includes(container.componentName)) {
        // lego自定义筛选器物料点击选中逻辑
        try {
          const { children = {} } = root || {};
          const symbols = Object.getOwnPropertySymbols(children);
          const childrenObj = children[symbols[0]] || {};
          const childrenList = childrenObj.children || [];
          const legoDefalutRowNode = childrenList.find(
            (n) => n.schema?.props?.isDefaultFilter,
          );
          project().currentDocument.insertNode(
            legoDefalutRowNode,
            cellData,
            legoDefalutRowNode.schema.children.length - 1,
          );
          this.aliLowCodeEngineEditor?.eventBus.emit(
            'lego.designer.schema.change',
          );
          root.document?.selection.selectAll(
            [legoDefalutRowNode].map((o) => {
              try {
                function getId(n: any = {}) {
                  const { id, componentName, schema, children } = n;
                  if (componentName === 'FDRow') {
                    return getId(schema.children[schema.children.length - 2]);
                  } else if (componentName === 'FDCell') {
                    return getId(schema ? schema.children[0] : children[0]);
                  } else {
                    return id;
                  }
                }
                return getId(o);
              } catch {
                return o.id;
              }
            }),
          );
        } catch (e) {
          reportBusinessMonitor(
            'legoBI-editDrop-warn',
            { error: e, isFilter: true },
            'warn',
          );
        }
      } else {
        // 乐高自定义非筛选器物料点击自动添加到最后一行末尾,并执行换行判断逻辑
        try {
          const selectNode = getSelectComponentNode();
          //判断当前选中的组件是否在tab 组件内
          TabNode = walkFromParentNode(selectNode?.parent, (node) => {
            if (node?.componentName === 'XTab') return node;
          });
          // 如果存在则将root 改为 tab下的 slot
          if (TabNode) {
            // 查找 slot
            root = getCurrentTabSlot(TabNode);
          }

          const { children = {} } = root || {};
          const symbols = Object.getOwnPropertySymbols(children);
          const childrenObj = children[symbols[0]] || {};
          const childrenList = childrenObj.children || [];
          const childrenListLength = childrenList.length;

          let selectNodes: any[] = [];

          // 当前选中为tab 组件, 要插入tab 内
          if (selectNode && selectNode.componentName === 'XTab') {
            // 查找 slot
            const curSlot = getCurrentTabSlot(selectNode);
            if (curSlot) {
              TabNode = selectNode;
              const node = project().currentDocument.insertNode(
                curSlot,
                rowData,
                curSlot?.children?.size || 0,
              );
              selectNodes = [node];
              // node.getDOMNode().scrollIntoView();
            }
          } else if (
            childrenListLength === 0 ||
            (childrenListLength === 1 &&
              (childrenList[0].propsData || {}).isDefaultFilter)
          ) {
            // 画布为空或者画布只有一行且为乐高默认筛选器
            project().currentDocument.insertNode(root, rowData);
            // 选中最后一行
            selectNodes = [childrenList[childrenList.length - 1]];
          } else {
            // 画布中最后一行
            const lastRow = childrenList[childrenList.length - 1] || {};
            const lastRowChildren = lastRow.schema?.children || [];
            project().currentDocument.insertNode(
              lastRow,
              cellData,
              lastRowChildren.length,
            );
            // 选中最后一行的最后一项
            const newLastRowChildren = lastRow.schema?.children || [];
            const { handleRowWidth } = window.AliLowCodeEngine || {};
            selectNodes = [newLastRowChildren[newLastRowChildren.length - 1]];
            // 换行判断逻辑
            const rowWidthCheckInfo = {
              detail: {
                index: newLastRowChildren.length - 1,
              },
              target: lastRow,
            };
            handleRowWidth(rowWidthCheckInfo, { isClickAdd: true });
          }

          // lego自定义非筛选器物料点击选中逻辑
          root.document?.selection.selectAll(
            selectNodes.map((o) => {
              try {
                function getId(n: any = {}) {
                  const { id, componentName, schema, children } = n;
                  if (componentName === 'FDRow') {
                    return getId(schema.children[0]);
                  } else if (componentName === 'FDCell') {
                    return getId(schema ? schema.children[0] : children[0]);
                  } else {
                    return id;
                  }
                }
                return getId(o);
              } catch {
                return o.id;
              }
            }),
          );
        } catch (e) {
          reportBusinessMonitor(
            'legoBI-editDrop-warn',
            { error: e, isFilter: false },
            'warn',
          );
        }
      }

      // 点击添加物料滑动到画布底部
      const legoIframeSimulatorRenderer = document.getElementById(
        'lego-iframe-simulatorRenderer',
      );
      if (
        legoIframeSimulatorRenderer &&
        legoIframeSimulatorRenderer.contentWindow
      ) {
        const innerDoc = legoIframeSimulatorRenderer.contentWindow.document;
        const legoEngineDocument = innerDoc.getElementById(
          'lego-engine-document',
        );
        legoEngineDocument.classList.add('legoALLOverflowHidden');
        setTimeout(() => {
          legoEngineDocument.classList.remove('legoALLOverflowHidden');
          // tab 组件不能进行滚动下面
          if (TabNode) {
            const slotDom = TabNode.getDOMNode().querySelector(
              '.xtab-item-wrapper-active',
            );
            // console.log('curSlot.getDOMNode()', selectNode.getDOMNode());
            slotDom?.scrollTo({
              top: slotDom?.children[0].clientHeight,
              behavior: 'smooth',
            });
          } else {
            if (filtersMarkers.includes(container.componentName)) {
              // lego自定义筛选器物料点击选中逻辑
              legoIframeSimulatorRenderer.contentWindow.scrollTo(0, 0);
            } else {
              // lego自定义非筛选器物料点击选中逻辑
              legoIframeSimulatorRenderer.contentWindow.scrollTo(
                0,
                legoEngineDocument.querySelector('.lce-page').offsetHeight,
              );
            }
          }
        }, 10);
      }
    };

    shell.addEventListener('click', throttle(click, 200));

    _dragon.from(shell, (e: Event) => {
      const doc = isNewEngineVersion()
        ? project().getCurrentDocument()
        : designer?.currentDocument;
      const id = getSnippetId(e.target);
      if (!doc || !id) {
        return false;
      }

      const container = this.store.getSnippetById(id);
      const fdCellStyle: any = {
        backgroundColor: 'rgba(255,255,255,1)',
      };
      // 为表格类物料容器添加overflow,解决列撑开画布问题
      try {
        const { components = [] } = window.LeopardWebQbiMeta || {};
        const curr = components.find(
          (c) => c.componentName === container.componentName,
        );
        if (
          (curr &&
            curr.componentBehavior &&
            curr.componentBehavior.componentType === 2) ||
          curr.componentName === 'Rank'
        ) {
          fdCellStyle['overflow'] = 'hidden';
        }
      } catch (e) {}
      const dragTarget = {
        type: 'nodedata',
        // data: this.store.getSnippetById(id),
        data: {
          componentName: 'FDCell',
          props: {
            align: 'center',
            verAlign: 'top',
            style: fdCellStyle,
            id: '__FDCell_New_Guide_ID',
          },
          title: '容器',
          hidden: false,
          isLocked: false,
          condition: true,
          conditionGroup: '',
          children: [container],
        },
      };

      return dragTarget;
    });

    shell.dataset.registered = 'true';
  };

  handleSearch = (keyword = '') => {
    this.setState({
      keyword: keyword.toLowerCase(),
    });
    this.getFilteredComponents();
  };

  renderEmptyContent() {
    return (
      <div className={'empty'}>
        <img src="//g.alicdn.com/uxcore/pic/empty.png" />
        <div className={'content'}>
          {this.t(
            createI18n(
              '暂无组件，请在物料站点添加',
              'No components, please add materials',
            ),
          )}
        </div>
      </div>
    );
  }

  // 物料下拉-鼠标进入
  onMouseOver(materiel, category) {
    const { groupName, groupId, materiels } = materiel;
    const cname = this.t(category.name);
    const id = `lego-materiel-${encodeURI(cname)}-${groupId}`;
    const ele = document.getElementById(id);
    if (ele.style.display === 'none') {
      ele.style.display = 'block';
    }
  }

  // 物料下拉-鼠标离开
  onMouseleave(materiel, category) {
    const { groupName, groupId, materiels } = materiel;
    const cname = this.t(category.name);
    const id = `lego-materiel-${encodeURI(cname)}-${groupId}`;
    const ele = document.getElementById(id);
    ele.style.display = 'none';
  }
  // 设置自定义物料分组
  setMaterielGroup(materiels = []) {
    // 忽略展示的自定义物料
    const ignoreMateriels = [
      'CapacityCompanyFilter',
      'CityFilter',
      'SearchButton',
      'DateFilterGlobal',
      'AiAttribution',
      'BrandFilter',
      'XTabItem',
      'NewCityFilter',
      'NewCarTeamFilter',
      'NewFleetFilter',
      'DjCityFilter',
      'DjFleetFilter',
    ];

    return this.materielGroup.map((v) => {
      const materielList = [...materiels]
        .filter((m) =>
          sdkConfig.get('sdkConfig.materialsSelect').includes(m.componentName),
        )
        .filter(
          (materiel) =>
            v.groupId === (materiel.componentBehavior || {}).componentType &&
            !ignoreMateriels.includes(materiel.componentName),
        )
        .sort(
          (a, b) =>
            a?.componentBehavior?.sortIndex - b?.componentBehavior?.sortIndex,
        );
      v.materiels = materielList || [];
      return v;
    });
  }

  renderContent() {
    const { filter = [] } = this.state;
    const { showComponents, icons } = this.props;
    const hasContent = filter.filter((item) => {
      return item?.categories?.filter((category) => {
        return category?.components?.length;
      }).length;
    }).length;
    // console.log(hasContent, '🌈🌈=========hasContent', keyword);
    if (!hasContent) {
      return this.renderEmptyContent();
    }

    // 自定义物料分组
    // componentType 组件分类 1:指标卡 2:表格 3:图表 4:筛选器 5:文本

    return (
      <>
        {filter.map((group) => {
          const { categories, name } = group;
          return (
            <>
              <div ref={this.registerAdditive} style={{ display: 'flex' }}>
                {categories.map((category) => {
                  const { components } = category;
                  const cname = this.t(category.name);
                  // console.log(components, '🌈🌈=========components', cname);

                  let list = [];
                  components.forEach((component) => {
                    const {
                      componentName,
                      componentBehavior = {},
                      snippets = [],
                    } = component;
                    if (showComponents.includes(componentName)) {
                      list.push(component);
                    }
                  });

                  const materielGroup = this.setMaterielGroup(list);

                  if (!materielGroup.length) {
                    return null;
                  }

                  {
                    return materielGroup.map((m) => {
                      const {
                        groupName,
                        groupIcon: GroupIcon,
                        groupId,
                        materiels,
                      } = m;
                      if (materiels.length > 1) {
                        // 分组物料数量大于1
                        return (
                          <div
                            className="lego-materiel-box"
                            key={groupId}
                            name={groupName}
                            onMouseOver={() => {
                              this.onMouseOver(m, category);
                            }}
                            onMouseLeave={() => {
                              this.onMouseleave(m, category);
                            }}
                          >
                            <div className="lego-materiel-title">
                              <div className="icon">
                                <GroupIcon></GroupIcon>
                              </div>
                              <div className="name">
                                {groupName}
                                <DownOutlined
                                  style={{ fontSize: '8px', marginLeft: '3px' }}
                                />
                              </div>
                            </div>
                            <div
                              id={`lego-materiel-${encodeURI(
                                cname,
                              )}-${groupId}`}
                              className="lego-materiel-content"
                              style={{ display: 'none' }}
                            >
                              <div
                                className={
                                  'contentBox ' +
                                  (materiels?.length > 4
                                    ? 'columns-2'
                                    : 'columns-1')
                                }
                              >
                                <>
                                  {materiels.map((component) => {
                                    const { componentName, snippets = [] } =
                                      component;
                                    if (
                                      !showComponents.includes(componentName)
                                    ) {
                                      return false;
                                    }
                                    return snippets
                                      .filter((snippet) => snippet.id)
                                      .map((snippet) => {
                                        return (
                                          <Component
                                            data={{
                                              title:
                                                snippet.title ||
                                                component.title,
                                              icon:
                                                icons[component.title] ||
                                                DragIcon,
                                              snippets: [snippet],
                                            }}
                                            t={this.t}
                                            key={`${this.t(
                                              group.name,
                                            )}_${this.t(
                                              componentName,
                                            )}_${this.t(snippet.title)}`}
                                          />
                                        );
                                      });
                                  })}
                                </>
                              </div>
                            </div>
                          </div>
                        );
                      } else if (materiels.length === 1) {
                        // 分组物料数量小于等于1
                        return (
                          <span
                            className="lego-materiel-box-single"
                            key={groupId}
                            name={groupName}
                          >
                            <>
                              {materiels.map((component) => {
                                const { componentName, snippets = [] } =
                                  component;
                                // console.log(
                                //   componentName,
                                //   '🌈🌈=========componentName',
                                // );
                                if (!showComponents.includes(componentName)) {
                                  return false;
                                }
                                return snippets
                                  .filter((snippet) => snippet.id)
                                  .map((snippet) => {
                                    // console.log(
                                    //   JSON.stringify(snippet),
                                    //   '🌈🌈=========snippet===',
                                    // );
                                    return (
                                      <Component
                                        data={{
                                          title: groupName,
                                          icon: GroupIcon,
                                          snippets: [snippet],
                                        }}
                                        t={this.t}
                                        key={`${this.t(group.name)}_${this.t(
                                          componentName,
                                        )}_${this.t(snippet.title)}`}
                                      />
                                    );
                                  });
                              })}
                            </>
                          </span>
                        );
                      }
                    });
                  }
                })}
              </div>
            </>
          );
        })}
      </>
    );
  }

  render() {
    return (
      <div className={'lowcode-component-panel'}>
        {this.renderContent()}
        <NewGuide
          steps={ComponentPane.steps}
          localKey="New_Guide_Lego_Component"
          visible={!this.props.disabled}
        />
      </div>
    );
  }
}

export const PaneIcon = IconOfPane;
